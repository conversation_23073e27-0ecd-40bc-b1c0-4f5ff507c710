'use client'
import {
  useEffect,
  useRef, useState,
} from "react";
import {
  acceptedImageMimeTypes,
  FILE_SIZE_10_MB,
} from "@/common/constants";
import { UploadIcon } from "@/common/components/icons";
import toast from "react-hot-toast";
import Image from "next/image";
import {
  CircularSpinner,
} from '@/common/components/atoms';
import { Edit } from "lucide-react";
import lang from "@/common/lang";

const { manageIdea: { imageUpload: imageUploadCopy } } = lang

export const PhotoUploader = ({
  id,
  loading,
  errorMessage,
  setValue,
  setIsUploadFromLocal,
  setSelectedFile,
  value,
} : {
  loading: boolean;
  errorMessage?: string;
  setValue: (value: string) => void;
  setIsUploadFromLocal: (value: boolean) => void;
  setSelectedFile: (value: File) => void;
  value: string;
  id: string;
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [error, setError] = useState('');
  const [imageUrl, setImageUrl] = useState(value || '');
  useEffect(() => {
    setImageUrl(value);
  }, [value]);

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      setValue('')
      const file = event.target.files[0];
      if (!file) {
        return;
      }
      if (file.size > FILE_SIZE_10_MB) {
        setError(imageUploadCopy.imageSizeError)
        return;
      }
      if (!acceptedImageMimeTypes.includes(file.type)) {
        setError(imageUploadCopy.imageType);
        return;
      }
      try {
        const data = new FormData();
        data.set("file", file);
        setError('')
        const imageUrl = URL.createObjectURL(file);
        setValue(imageUrl);
        setSelectedFile(file)
        setIsUploadFromLocal(true)
      } catch (e) {
        setError(imageUploadCopy.uploadError)
        toast.error(imageUploadCopy.uploadError)
      }
    }
  }

  const handleUploadFile = () => {
    fileInputRef.current?.click();
  }

  const handleInputClick = (event: React.MouseEvent<HTMLInputElement, MouseEvent>) => {
    const element = event.target as HTMLInputElement;
    element.value = '';
  }
  return (
    <div className="flex flex-col mt-4">
      <label
        htmlFor={id}
        className="text-left w-max text-white font-medium text-sm"
      >
        {imageUploadCopy.postImage}
      </label>
      <input
        type="file"
        name="file"
        accept={acceptedImageMimeTypes.join(', ')}
        id={id}
        ref={fileInputRef}
        className="opacity-0 -z-10 absolute overflow-hidden"
        onChange={handleFileChange}
        onClick={handleInputClick}
      />
      {loading ? (
        <div
          className="bg-white/5 backdrop-blur-sm text-white font-medium text-sm border border-white/5 mt-2 group uploadIconWithGradient disabled:pointer-events-none transition-transform duration-200 px-4 py-8 rounded-2xl flex flex-col gap-2 items-center"
        >
          <CircularSpinner />
          Generating...
        </div>
      ) : (
        imageUrl?.length ? (
          <div className="flex gap-2 relative">
            <div className="w-full h-auto rounded-2xl border border-white/5 overflow-hidden mt-2 bg-white/5 backdrop-blur-sm relative flex justify-center items-center">
              <Image
                src={imageUrl}
                alt="pinata"
                width={400}
                height={200}
                className="w-full h-auto md:max-h-[280px] object-contain rounded-md"
                quality={50}
              />
            </div>
            <button
              onClick={handleUploadFile}
              type="button"
              className="shadow-sm absolute top-6 right-4 p-1 rounded-lg bg-violets-are-blue disabled:bg-gray-400 disabled:cursor-not-allowed hover:bg-violets-are-blue/80 text-white"
            >
              <Edit width={16} height={16} />
            </button>
          </div>
        ) : (
          <button
            type="button"
            className="bg-white/5 backdrop-blur-sm border border-white/5 mt-2 group uploadIconWithGradient disabled:pointer-events-none transition-transform duration-200 px-4 py-8 rounded-2xl flex flex-col gap-1 items-center"
            onClick={handleUploadFile}
          >
            <UploadIcon />
            <span className="text-neutral-300 text-sm font-medium group-hover:text-white">
              {imageUploadCopy.uploadLabel}
            </span>
          </button>
        )
      )}
      {(error) && (
        <p className="mt-0.5 text-sm text-tulip">
          {errorMessage || error || ''}
        </p>
      )}
    </div>
  )
}
