'use client'

import {
  useState, useRef, ReactNode, useEffect,
} from 'react'
import {
  motion, AnimatePresence,
} from 'framer-motion'
import { useOutsideClick } from '@/common/hooks'
import { cn } from '@/common/utils/helpers'
import { ChevronDown } from 'lucide-react'
import { Portal } from '@/common/components/atoms/Portal'

export type DropdownOption = {
  label: string;
  [key: string]: any;
}

interface DropdownProps {
  options: DropdownOption[];
  selectedOption?: DropdownOption | null;
  onSelect: (option: DropdownOption) => void;
  placeholder?: string;
  label?: string;
  className?: string;
  buttonClassName?: string;
  dropdownClassName?: string;
  optionClassName?: string;
  icon?: ReactNode;
}

export const Dropdown = ({
  options,
  selectedOption,
  onSelect,
  placeholder = 'Select an option',
  label,
  className,
  buttonClassName,
  dropdownClassName,
  optionClassName,
  icon,
}: DropdownProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const buttonRef = useRef<HTMLButtonElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const [dropdownPosition, setDropdownPosition] = useState({
    top: 0,
    left: 0,
    width: 0,
  })

  useEffect(() => {
    if (isOpen && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect()
      setDropdownPosition({
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width,
      })
    }
  }, [isOpen])

  useOutsideClick({
    ref: dropdownRef,
    callback: () => setIsOpen(false),
    isVisible: isOpen,
  })

  const handleOptionClick = (option: DropdownOption) => {
    onSelect(option);
    setIsOpen(false);
  };

  return (
    <div className={cn("w-full", className)}>
      {label && <div className='text-white font-medium text-sm mb-1'>{label}</div>}
      <button
        ref={buttonRef}
        type="button"
        className={cn(
          "w-full rounded-xl py-2 px-3 text-sm text-white bg-white/5 border border-white/5 backdrop-blur-sm hover:border-violets-are-blue focus:border-violets-are-blue flex justify-between items-center",
          buttonClassName,
        )}
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className={selectedOption ? "text-white" : "text-gray-600"}>
          {selectedOption ? selectedOption.label : placeholder}
        </span>
        <span className={`text-white cursor-pointer transition-transform duration-200 ${isOpen ? "rotate-180" : ""}`}>
          {icon || <ChevronDown size={18} />}
        </span>
      </button>

      <AnimatePresence>
        {isOpen && (
          <Portal>
            <div
              ref={dropdownRef}
              style={{
                position: 'absolute',
                top: `${dropdownPosition.top}px`,
                left: `${dropdownPosition.left}px`,
                width: `${dropdownPosition.width}px`,
                zIndex: 9999,
              }}
            >
              <motion.div
                initial={{
                  opacity: 0,
                  y: -10,
                }}
                animate={{
                  opacity: 1,
                  y: 0,
                }}
                exit={{
                  opacity: 0,
                  y: -10,
                }}
                transition={{ duration: 0.2 }}
                className={cn(
                  "w-full mt-1 rounded-xl overflow-hidden bg-eerie-black border border-white/5",
                  dropdownClassName,
                )}
              >
                <ul className="py-1 max-h-64 overflow-auto">
                  {options.map((option, index) => (
                    <li
                      key={index}
                      className={cn(
                        "px-3 py-2 text-sm text-neutral-400 hover:text-black hover:bg-white/70 transition-all duration-200 ease-in-out cursor-pointer",
                        optionClassName,
                      )}
                      onClick={() => handleOptionClick(option)}
                    >
                      {option.label}
                    </li>
                  ))}
                </ul>
              </motion.div>
            </div>
          </Portal>
        )}
      </AnimatePresence>
    </div>
  )
}
