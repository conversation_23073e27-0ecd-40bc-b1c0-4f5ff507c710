'use client'

import {
  useState, useEffect,
} from 'react';
import { CanvasEditor } from '@/common/components/organisms';
import toast from 'react-hot-toast';

interface ImageModalProps {
  isOpen: boolean;
  onClose: () => void;
  agentId: string;
  planId: string;
  content?: string;
  onImageAttached: (imageUrl: string, isFromAI: boolean, file?: File) => void;
}

export const ImageModal = ({
  isOpen,
  onClose,
  agentId,
  planId,
  content = '',
  onImageAttached,
}: ImageModalProps) => {
  const [imagePrompt, setImagePrompt] = useState(content.trim() || '');
  const [currentImage, setCurrentImage] = useState('');
  const [isUploadFromLocal, setIsUploadFromLocal] = useState(false);

  useEffect(() => {
    setImagePrompt(content.trim() || '');
  }, [content, isOpen]);

  const handleCanvasEditorSave = (imageUrl: string) => {
    setCurrentImage(imageUrl);
    setIsUploadFromLocal(false);
    onClose();
    toast.success('Media saved!');
  };

  return (
    <CanvasEditor
      isOpen={isOpen}
      onClose={() => onClose()}
      onSave={handleCanvasEditorSave}
      initialImage={currentImage || undefined}
      agentId={agentId}
      planId={planId}
    />
  );
};

export default ImageModal;
