'use client'

import React from 'react';
import { Button } from '@/common/components/atoms';
import {
  Undo2,
  Redo2,
} from 'lucide-react';

interface CanvasToolbarProps {
  onClose: () => void;
  onSaveDesign: () => void;
  onUndo: () => void;
  onRedo: () => void;
  canUndo: boolean;
  canRedo: boolean;
}

export const CanvasToolbar = ({
  onClose,
  onSaveDesign,
  onUndo,
  onRedo,
  canUndo,
  canRedo,
}: CanvasToolbarProps) => {

  return (
    <div className="bg-eerie-black border-b border-neutral-700 px-2 md:px-4 py-2 md:py-3 flex items-center justify-between relative">
      <div className="flex items-center gap-1 md:gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={onUndo}
          disabled={!canUndo}
          className="flex items-center gap-1"
        >
          <Undo2 size={16} />
          <span className="hidden md:inline">Undo</span>
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={onRedo}
          disabled={!canRedo}
          className="flex items-center gap-1"
        >
          <Redo2 size={16} />
          <span className="hidden md:inline">Redo</span>
        </Button>
      </div>
      <div className="absolute  left-1/2 transform -translate-x-1/2 text-center">
        <h1 className="text-white font-semibold text-sm md:text-lg">Image Editor</h1>
      </div>
      <div className="flex items-center gap-1 md:gap-2">
        <Button
          variant="gradient"
          size="sm"
          onClick={onSaveDesign}
        >
          Save
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={onClose}
        >
          Close
        </Button>
      </div>
    </div>
  );
};
